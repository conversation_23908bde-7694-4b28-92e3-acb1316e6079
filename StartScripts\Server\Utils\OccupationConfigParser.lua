--!strict
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local OccupationConfig = require(ReplicatedStorage.Scripts.Config.OccupationConfig)

local OccupationConfigParser = {}

-- 根据职业ID创建配置索引，提高查询效率
local ConfigById = {}
for _, config in ipairs(OccupationConfig) do
	ConfigById[config.Id] = config
end

-- 解析单个物品字符串 (格式: "数量_类型_物品ID")
-- @param itemStr: 物品字符串，例如 "2_7_10026"
-- @return: 解析后的物品数据表 {quantity: number, itemType: number, id: number}
local function parseItemString(itemStr: string)
	local parts = string.split(itemStr, "_")
	if #parts ~= 3 then
		warn("物品字符串格式错误: " .. itemStr .. " (期望格式: 数量_类型_物品ID)")
		return nil
	end
	
	local quantity = tonumber(parts[1])
	local itemType = tonumber(parts[2])
	local id = tonumber(parts[3])
	
	if not quantity or not itemType or not id then
		warn("物品字符串包含无效数字: " .. itemStr)
		return nil
	end
	
	return {
		quantity = quantity,
		itemType = itemType,
		id = id
	}
end

-- 解析CarryingItem字段 (格式: "2_7_10026;50_6_10029;1_10_10051")
-- @param carryingItemStr: CarryingItem字符串
-- @return: 解析后的物品数组
local function parseCarryingItems(carryingItemStr: string)
	if not carryingItemStr or carryingItemStr == "" then
		return {}
	end
	
	local items = {}
	local itemStrings = string.split(carryingItemStr, ";")
	
	for _, itemStr in ipairs(itemStrings) do
		local item = parseItemString(itemStr)
		if item then
			table.insert(items, item)
		end
	end
	
	return items
end

-- 根据职业ID获取职业配置
-- @param occupationId: 职业ID
-- @return: 职业配置数据或nil
function OccupationConfigParser.getOccupationConfig(occupationId: number)
	return ConfigById[occupationId]
end

-- 根据职业ID获取物品数据
-- @param occupationId: 职业ID
-- @return: 物品数据数组
function OccupationConfigParser.getItemsByOccupationId(occupationId: number)
	local config = ConfigById[occupationId]
	if not config then
		warn("未找到职业ID为 " .. occupationId .. " 的配置")
		return {}
	end
	
	if not config.CarryingItem then
		warn("职业ID " .. occupationId .. " 没有CarryingItem数据")
		return {}
	end
	
	return parseCarryingItems(config.CarryingItem)
end

-- 获取所有职业的物品数据（用于调试）
-- @return: 包含所有职业物品数据的表
function OccupationConfigParser.getAllOccupationItems()
	local allItems = {}
	for occupationId, config in pairs(ConfigById) do
		allItems[occupationId] = {
			occupationId = occupationId,
			items = parseCarryingItems(config.CarryingItem or "")
		}
	end
	return allItems
end

-- 验证物品数据格式是否正确
-- @param carryingItemStr: CarryingItem字符串
-- @return: 是否有效
function OccupationConfigParser.validateCarryingItemFormat(carryingItemStr: string)
	if not carryingItemStr or carryingItemStr == "" then
		return true -- 空字符串被认为是有效的
	end
	
	local itemStrings = string.split(carryingItemStr, ";")
	for _, itemStr in ipairs(itemStrings) do
		local parts = string.split(itemStr, "_")
		if #parts ~= 3 then
			return false
		end
		
		local quantity = tonumber(parts[1])
		local itemType = tonumber(parts[2])
		local id = tonumber(parts[3])
		
		if not quantity or not itemType or not id then
			return false
		end
	end
	
	return true
end

-- 打印职业物品数据（用于调试）
-- @param occupationId: 职业ID，如果为nil则打印所有职业
function OccupationConfigParser.printOccupationItems(occupationId: number?)
	if occupationId then
		local items = OccupationConfigParser.getItemsByOccupationId(occupationId)
		print("=== 职业ID " .. occupationId .. " 的物品数据 ===")
		for i, item in ipairs(items) do
			print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
		end
		print("=== 结束 ===")
	else
		local allItems = OccupationConfigParser.getAllOccupationItems()
		print("=== 所有职业物品数据 ===")
		for id, data in pairs(allItems) do
			print("职业ID " .. id .. ":")
			for i, item in ipairs(data.items) do
				print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
			end
		end
		print("=== 结束 ===")
	end
end

return OccupationConfigParser
