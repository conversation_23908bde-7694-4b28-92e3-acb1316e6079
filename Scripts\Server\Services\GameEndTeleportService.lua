--!strict
-- 游戏结束传送服务
-- 负责在全员濒死后10秒传送所有玩家回初始场景

local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local GameEndTeleportService = {}

-- 配置
local INITIAL_PLACE_ID = nil -- 将在运行时从传送数据中获取
local COUNTDOWN_TIME = 10 -- 10秒倒计时
local FALLBACK_PLACE_ID = 89908111666289 -- 备用场景ID（如果无法获取初始场景ID）

-- 状态管理
local isCountdownActive = false
local countdownStartTime = 0
local countdownConnection = nil

-- 初始化服务
function GameEndTeleportService:Initialize()
	print("GameEndTeleportService 初始化开始")
	
	-- 尝试从传送数据中获取初始场景ID
	self:DetectInitialPlaceId()
	
	print("GameEndTeleportService 初始化完成")
end

-- 检测初始场景ID
function GameEndTeleportService:DetectInitialPlaceId()
	-- 尝试从任意玩家的传送数据中获取源场景ID
	local TeleportDataManager = require(ReplicatedStorage.Scripts.Server.Services.TeleportDataManager)
	
	for _, player in ipairs(Players:GetPlayers()) do
		local teleportData = TeleportDataManager:GetPlayerTeleportData(player)
		if teleportData and teleportData.sourcePlace then
			INITIAL_PLACE_ID = teleportData.sourcePlace
			print("✅ 检测到初始场景ID:", INITIAL_PLACE_ID)
			return
		end
	end
	
	-- 如果无法获取，使用当前场景ID作为备用
	if not INITIAL_PLACE_ID then
		INITIAL_PLACE_ID = FALLBACK_PLACE_ID
		warn("⚠️ 无法检测初始场景ID，使用备用场景ID:", INITIAL_PLACE_ID)
	end
end

-- 开始全员濒死倒计时
function GameEndTeleportService:StartAllDownedCountdown()
	if isCountdownActive then
		print("⚠️ 倒计时已在进行中，忽略重复请求")
		return
	end
	
	print("🚨 开始全员濒死倒计时，" .. COUNTDOWN_TIME .. "秒后传送回初始场景")
	
	-- 确保有初始场景ID
	if not INITIAL_PLACE_ID then
		self:DetectInitialPlaceId()
	end
	
	isCountdownActive = true
	countdownStartTime = tick()
	
	-- 立即通知所有客户端开始倒计时
	NotifyService.FireAllClient("AllDownedCountdownStart", {
		countdownTime = COUNTDOWN_TIME,
		targetPlaceId = INITIAL_PLACE_ID,
		startTime = countdownStartTime
	})
	
	-- 开始倒计时循环
	countdownConnection = spawn(function()
		local lastSecond = COUNTDOWN_TIME
		
		while isCountdownActive do
			local elapsed = tick() - countdownStartTime
			local remaining = COUNTDOWN_TIME - elapsed
			local currentSecond = math.ceil(remaining)
			
			-- 每秒更新一次
			if currentSecond ~= lastSecond and currentSecond >= 0 then
				lastSecond = currentSecond
				
				-- 通知客户端更新倒计时显示
				NotifyService.FireAllClient("AllDownedCountdownUpdate", {
					remainingTime = currentSecond,
					totalTime = COUNTDOWN_TIME
				})
				
				print("⏰ 倒计时: " .. currentSecond .. "秒")
			end
			
			-- 倒计时结束
			if remaining <= 0 then
				self:ExecuteTeleportToInitial()
				break
			end
			
			wait(0.1)
		end
	end)
end

-- 停止倒计时（如果有玩家复活）
function GameEndTeleportService:StopAllDownedCountdown()
	if not isCountdownActive then
		return
	end
	
	print("🛑 停止全员濒死倒计时（有玩家复活）")
	
	isCountdownActive = false
	
	-- 停止倒计时协程
	if countdownConnection then
		countdownConnection = nil
	end
	
	-- 通知所有客户端停止倒计时
	NotifyService.FireAllClient("AllDownedCountdownStop", {
		reason = "playerRevived"
	})
end

-- 执行传送回初始场景
function GameEndTeleportService:ExecuteTeleportToInitial()
	print("🚀 执行传送回初始场景，目标场景ID:", INITIAL_PLACE_ID)
	
	isCountdownActive = false
	
	-- 获取所有在线玩家
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("❌ 没有玩家需要传送")
		return
	end
	
	-- 通知客户端即将传送
	NotifyService.FireAllClient("TeleportToInitialStart", {
		targetPlaceId = INITIAL_PLACE_ID,
		playerCount = #allPlayers
	})
	
	-- 准备传送数据（保持当前状态，不传送物品数据）
	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		returnReason = "allPlayersDowned", -- 标记返回原因
		returnTime = os.date("%Y-%m-%d %H:%M:%S", os.time())
	})
	
	-- 执行传送
	local success, errorMessage = pcall(function()
		TeleportService:TeleportAsync(INITIAL_PLACE_ID, allPlayers, teleportOptions)
	end)
	
	if success then
		print("✅ 传送成功，已将 " .. #allPlayers .. " 个玩家传送回初始场景")
	else
		warn("❌ 传送失败:", errorMessage)
		
		-- 传送失败时通知客户端
		NotifyService.FireAllClient("TeleportToInitialFailed", {
			error = errorMessage,
			targetPlaceId = INITIAL_PLACE_ID
		})
	end
end

-- 检查是否应该开始倒计时
function GameEndTeleportService:CheckShouldStartCountdown()
	-- 检查是否所有玩家都濒死
	local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)
	
	local allPlayersDowned = true
	local totalPlayers = 0
	local downedPlayers = 0
	
	for _, player in ipairs(Players:GetPlayers()) do
		totalPlayers = totalPlayers + 1
		if PlayerDownedService:IsPlayerDowned(player) then
			downedPlayers = downedPlayers + 1
		else
			allPlayersDowned = false
		end
	end
	
	print("🔍 检查倒计时条件: " .. downedPlayers .. "/" .. totalPlayers .. " 玩家濒死")
	
	-- 只有在所有玩家都濒死且有玩家在线时才开始倒计时
	if allPlayersDowned and totalPlayers > 0 and not isCountdownActive then
		self:StartAllDownedCountdown()
		return true
	elseif not allPlayersDowned and isCountdownActive then
		-- 如果有玩家复活，停止倒计时
		self:StopAllDownedCountdown()
		return false
	end
	
	return false
end

-- 获取倒计时状态
function GameEndTeleportService:GetCountdownStatus()
	if not isCountdownActive then
		return {
			active = false,
			remaining = 0,
			total = COUNTDOWN_TIME
		}
	end
	
	local elapsed = tick() - countdownStartTime
	local remaining = math.max(0, COUNTDOWN_TIME - elapsed)
	
	return {
		active = true,
		remaining = remaining,
		total = COUNTDOWN_TIME,
		targetPlaceId = INITIAL_PLACE_ID
	}
end

-- 手动触发传送（用于测试或管理员命令）
function GameEndTeleportService:ManualTeleportToInitial()
	print("🔧 手动触发传送回初始场景")
	
	-- 停止当前倒计时（如果有）
	if isCountdownActive then
		self:StopAllDownedCountdown()
	end
	
	-- 立即执行传送
	self:ExecuteTeleportToInitial()
end

return GameEndTeleportService
