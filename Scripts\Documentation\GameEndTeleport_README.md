# 全员濒死自动传送系统

## 📋 功能概述

本系统实现了**所有玩家濒死后10秒自动传送回初始场景**的功能。当所有在线玩家都进入濒死状态时，系统会自动开始10秒倒计时，并在倒计时结束后将所有玩家传送回初始场景。

## 🔄 工作流程

1. **濒死检测**：系统持续监控所有玩家的濒死状态
2. **全员濒死触发**：当检测到所有在线玩家都处于濒死状态时，触发倒计时
3. **倒计时显示**：客户端显示10秒倒计时UI，并播放相应的视觉和音效
4. **传送执行**：倒计时结束后，所有玩家被传送回初始场景
5. **取消机制**：如果在倒计时期间有玩家复活，倒计时会自动取消

## 🧩 系统组件

### 服务端组件

1. **GameEndTeleportService**
   - 核心服务，负责倒计时管理和传送逻辑
   - 自动检测初始场景ID
   - 处理倒计时启动、更新和停止
   - 执行传送操作

2. **PlayerDownedService 修改**
   - 集成了GameEndTeleportService
   - 在检测到全员濒死时触发倒计时
   - 在玩家复活时取消倒计时

3. **GameEndTeleportCommands**
   - 提供管理员命令用于测试和控制传送功能
   - 支持手动触发倒计时和传送
   - 提供状态查询和调试功能

### 客户端组件

1. **GameEndTeleportClient**
   - 负责显示倒计时UI
   - 处理服务端事件
   - 提供视觉和音效反馈

2. **PlayerDownedClient 修改**
   - 集成了GameEndTeleportClient
   - 在收到全员濒死通知时隐藏自我复活UI
   - 优化用户体验流程

## 🔧 技术实现

### 倒计时机制

```lua
-- 开始倒计时
function GameEndTeleportService:StartAllDownedCountdown()
    isCountdownActive = true
    countdownStartTime = tick()
    
    -- 通知客户端开始倒计时
    NotifyService.FireAllClient("AllDownedCountdownStart", {
        countdownTime = COUNTDOWN_TIME,
        targetPlaceId = INITIAL_PLACE_ID,
        startTime = countdownStartTime
    })
    
    -- 开始倒计时循环
    countdownConnection = spawn(function()
        -- 倒计时逻辑
        -- ...
    end)
end
```

### 传送实现

```lua
-- 执行传送
function GameEndTeleportService:ExecuteTeleportToInitial()
    -- 准备传送数据
    local teleportOptions = Instance.new("TeleportOptions")
    teleportOptions:SetTeleportData({
        sourcePlace = game.PlaceId,
        timestamp = os.time(),
        returnReason = "allPlayersDowned"
    })
    
    -- 执行传送
    TeleportService:TeleportAsync(INITIAL_PLACE_ID, allPlayers, teleportOptions)
end
```

### 初始场景ID检测

```lua
-- 检测初始场景ID
function GameEndTeleportService:DetectInitialPlaceId()
    -- 从传送数据中获取源场景ID
    for _, player in ipairs(Players:GetPlayers()) do
        local teleportData = TeleportDataManager:GetPlayerTeleportData(player)
        if teleportData and teleportData.sourcePlace then
            INITIAL_PLACE_ID = teleportData.sourcePlace
            return
        end
    end
    
    -- 如果无法获取，使用备用ID
    INITIAL_PLACE_ID = FALLBACK_PLACE_ID
end
```

## 📱 用户界面

系统提供了清晰的视觉反馈：

1. **倒计时UI**
   - 显示剩余时间
   - 进度条指示器
   - 传送目的地信息

2. **视觉效果**
   - 最后5秒倒计时闪烁效果
   - 传送前的屏幕过渡效果
   - 响应式UI动画

3. **状态提示**
   - 全员濒死状态提示
   - 传送准备中提示
   - 传送失败错误信息

## 🧪 测试功能

系统提供了全面的测试工具：

1. **管理员命令**
   - `/teleporttest [quick|full]` - 运行传送功能测试
   - `/alldown` - 模拟所有玩家濒死
   - `/reviveall` - 复活所有玩家
   - `/countdown [start|stop|status]` - 管理倒计时
   - `/teleportnow` - 立即传送回初始场景
   - `/teleporthelp` - 显示帮助信息

2. **测试脚本**
   - `GameEndTeleportTest.runFullTest()` - 运行完整测试套件
   - `GameEndTeleportTest.runQuickTest()` - 快速测试核心功能
   - `GameEndTeleportTest.emergencyStop()` - 紧急停止所有测试

## 🔄 事件系统

系统使用以下事件进行通信：

1. **服务端到客户端**
   - `AllPlayersDowned` - 通知所有玩家濒死
   - `AllDownedCountdownStart` - 开始倒计时
   - `AllDownedCountdownUpdate` - 更新倒计时
   - `AllDownedCountdownStop` - 停止倒计时
   - `TeleportToInitialStart` - 开始传送
   - `TeleportToInitialFailed` - 传送失败

2. **客户端到服务端**
   - 使用现有的濒死和复活事件

## 🛠️ 配置选项

系统提供以下可配置选项：

1. **倒计时时间**
   - 默认为10秒，可在`GameEndTeleportService.lua`中修改`COUNTDOWN_TIME`变量

2. **备用场景ID**
   - 当无法检测到初始场景ID时使用
   - 可在`GameEndTeleportService.lua`中修改`FALLBACK_PLACE_ID`变量

3. **管理员列表**
   - 可在`GameEndTeleportCommands.lua`中修改`ADMIN_USER_IDS`表

## 📝 使用说明

### 玩家体验

1. 当所有玩家都进入濒死状态时，系统会自动显示倒计时UI
2. 倒计时期间，如果有玩家被复活，倒计时会自动取消
3. 倒计时结束后，所有玩家将被传送回初始场景

### 管理员功能

1. 使用`/teleporthelp`查看所有可用命令
2. 使用`/teleporttest quick`快速测试传送功能
3. 使用`/teleportnow`手动触发传送（不等待倒计时）

## 🔍 故障排除

### 常见问题

1. **倒计时不启动**
   - 检查是否所有玩家都处于濒死状态
   - 使用`/countdown status`查看倒计时状态
   - 尝试使用`/countdown start`手动启动

2. **传送失败**
   - 检查初始场景ID是否正确
   - 确保目标场景存在且可访问
   - 查看服务器日志获取详细错误信息

3. **UI不显示**
   - 确保客户端服务已正确初始化
   - 检查NotifyService事件是否正常工作
   - 尝试重新加入游戏

## 🔄 与现有系统的集成

本系统与以下现有系统进行了集成：

1. **濒死系统**
   - 使用现有的濒死检测机制
   - 扩展了全员濒死的处理逻辑

2. **传送系统**
   - 利用现有的TeleportService功能
   - 保持与传送数据格式的兼容性

3. **UI系统**
   - 与现有的UI风格保持一致
   - 优化了用户体验流程

## 🚀 未来改进

1. **可配置传送目标**
   - 允许设置不同的传送目标场景
   - 支持基于游戏模式的动态目标选择

2. **高级UI效果**
   - 添加更丰富的视觉效果
   - 支持自定义UI主题

3. **数据保存**
   - 记录传送统计信息
   - 支持玩家传送历史查询

## 📚 相关文件

- `Scripts/Server/Services/GameEndTeleportService.lua` - 核心服务端逻辑
- `Scripts/Client/Services/GameEndTeleportClient.lua` - 客户端UI和事件处理
- `Scripts/Server/Commands/GameEndTeleportCommands.lua` - 管理员命令
- `Scripts/Test/GameEndTeleportTest.lua` - 测试脚本
- `Scripts/Documentation/GameEndTeleport_README.md` - 本文档
