--!strict
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local OccupationConfigParser = require(ReplicatedStorage.Scripts.Server.Utils.OccupationConfigParser)

local PlayerItemManager = {}

-- 玩家物品数据表：存储每个玩家的物品数据
-- 结构: {[userId] = {playerName: string, occupationId: number, items: {物品数据数组}}}
PlayerItemManager.playerItemData = {}

-- 为玩家设置职业物品数据
-- @param player: 玩家对象
-- @param occupationId: 职业ID
-- @return: 是否成功设置
function PlayerItemManager.setPlayerOccupationItems(player, occupationId: number)
	if not player then
		warn("PlayerItemManager.setPlayerOccupationItems: 玩家对象为空")
		return false
	end
	
	if not occupationId then
		warn("PlayerItemManager.setPlayerOccupationItems: 职业ID为空")
		return false
	end
	
	-- 获取职业对应的物品数据
	local items = OccupationConfigParser.getItemsByOccupationId(occupationId)
	if not items or #items == 0 then
		warn("PlayerItemManager.setPlayerOccupationItems: 职业ID " .. occupationId .. " 没有物品数据")
		-- 即使没有物品也要记录，避免数据不一致
		items = {}
	end
	
	local userId = player.UserId
	local playerName = player.Name
	
	-- 存储玩家物品数据
	PlayerItemManager.playerItemData[userId] = {
		playerName = playerName,
		occupationId = occupationId,
		items = items,
		timestamp = os.time() -- 记录设置时间
	}
	
	print("PlayerItemManager: 为玩家 " .. playerName .. " (ID:" .. userId .. ") 设置职业 " .. occupationId .. " 的物品数据，共 " .. #items .. " 个物品")
	
	-- 打印物品详情用于调试
	for i, item in ipairs(items) do
		print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
	end
	
	return true
end

-- 获取玩家的物品数据
-- @param userId: 玩家用户ID
-- @return: 玩家物品数据或nil
function PlayerItemManager.getPlayerItemData(userId: number)
	return PlayerItemManager.playerItemData[userId]
end

-- 获取玩家的物品列表
-- @param userId: 玩家用户ID
-- @return: 物品数组或空数组
function PlayerItemManager.getPlayerItems(userId: number)
	local data = PlayerItemManager.playerItemData[userId]
	if data then
		return data.items
	end
	return {}
end

-- 清除玩家的物品数据
-- @param userId: 玩家用户ID
function PlayerItemManager.clearPlayerItemData(userId: number)
	if PlayerItemManager.playerItemData[userId] then
		local playerName = PlayerItemManager.playerItemData[userId].playerName
		PlayerItemManager.playerItemData[userId] = nil
		print("PlayerItemManager: 清除玩家 " .. playerName .. " (ID:" .. userId .. ") 的物品数据")
	end
end

-- 获取所有玩家的物品数据（用于传送）
-- @return: 包含所有玩家物品数据的表
function PlayerItemManager.getAllPlayerItemData()
	return PlayerItemManager.playerItemData
end

-- 获取格式化的传送数据
-- @return: 适用于传送的物品数据格式
function PlayerItemManager.getTeleportItemData()
	local teleportData = {}
	
	for userId, data in pairs(PlayerItemManager.playerItemData) do
		teleportData[userId] = {
			playerName = data.playerName,
			occupationId = data.occupationId,
			items = data.items
		}
	end
	
	return teleportData
end

-- 打印所有玩家的物品数据（用于调试）
function PlayerItemManager.printAllPlayerItemData()
	print("=== 所有玩家物品数据 ===")
	
	if next(PlayerItemManager.playerItemData) == nil then
		print("  没有玩家物品数据")
		print("=== 结束 ===")
		return
	end
	
	for userId, data in pairs(PlayerItemManager.playerItemData) do
		print("玩家: " .. data.playerName .. " (ID:" .. userId .. ")")
		print("  职业ID: " .. data.occupationId)
		print("  设置时间: " .. os.date("%Y-%m-%d %H:%M:%S", data.timestamp))
		print("  物品数量: " .. #data.items)
		
		for i, item in ipairs(data.items) do
			print(string.format("    物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
		end
		print("  ---")
	end
	
	print("=== 结束 ===")
end

-- 打印指定玩家的物品数据
-- @param userId: 玩家用户ID
function PlayerItemManager.printPlayerItemData(userId: number)
	local data = PlayerItemManager.playerItemData[userId]
	if not data then
		print("PlayerItemManager: 玩家 ID:" .. userId .. " 没有物品数据")
		return
	end
	
	print("=== 玩家物品数据 ===")
	print("玩家: " .. data.playerName .. " (ID:" .. userId .. ")")
	print("职业ID: " .. data.occupationId)
	print("设置时间: " .. os.date("%Y-%m-%d %H:%M:%S", data.timestamp))
	print("物品数量: " .. #data.items)
	
	for i, item in ipairs(data.items) do
		print(string.format("  物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
	end
	
	print("=== 结束 ===")
end

-- 验证玩家物品数据的完整性
-- @param userId: 玩家用户ID
-- @return: 验证结果和错误信息
function PlayerItemManager.validatePlayerItemData(userId: number)
	local data = PlayerItemManager.playerItemData[userId]
	if not data then
		return false, "玩家物品数据不存在"
	end
	
	if not data.playerName or data.playerName == "" then
		return false, "玩家名字为空"
	end
	
	if not data.occupationId or data.occupationId <= 0 then
		return false, "职业ID无效"
	end
	
	if not data.items then
		return false, "物品数据为空"
	end
	
	-- 验证每个物品的数据完整性
	for i, item in ipairs(data.items) do
		if not item.id or not item.quantity or not item.itemType then
			return false, "物品" .. i .. "数据不完整"
		end
		
		if item.quantity <= 0 then
			return false, "物品" .. i .. "数量无效"
		end
	end
	
	return true, "数据验证通过"
end

-- 当玩家离开游戏时清理数据
-- @param player: 玩家对象
function PlayerItemManager.onPlayerLeaving(player)
	if player then
		PlayerItemManager.clearPlayerItemData(player.UserId)
	end
end

-- 初始化PlayerItemManager
function PlayerItemManager.InitServer()
	-- 监听玩家离开事件，自动清理数据
	game.Players.PlayerRemoving:Connect(PlayerItemManager.onPlayerLeaving)
	print("PlayerItemManager: 服务器初始化完成")
end

return PlayerItemManager
