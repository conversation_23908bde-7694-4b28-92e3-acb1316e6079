--!strict
-- 测试职业系统的完整功能
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local OccupationConfigParser = require(ReplicatedStorage.Scripts.Server.Utils.OccupationConfigParser)
local PlayerItemManager = require(ReplicatedStorage.Scripts.Server.Manager.PlayerItemManager)
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local TeleportManager = require(ReplicatedStorage.Scripts.Server.Manager.TeleportManager)

local TestOccupationSystem = {}

-- 测试OccupationConfigParser
function TestOccupationSystem.testOccupationConfigParser()
	print("=== 测试 OccupationConfigParser ===")
	
	-- 测试获取职业配置
	local config4001 = OccupationConfigParser.getOccupationConfig(4001)
	local config4002 = OccupationConfigParser.getOccupationConfig(4002)
	local configInvalid = OccupationConfigParser.getOccupationConfig(9999)
	
	print("职业4001配置:", config4001 and "存在" or "不存在")
	print("职业4002配置:", config4002 and "存在" or "不存在")
	print("无效职业配置:", configInvalid and "存在" or "不存在")
	
	-- 测试获取物品数据
	local items4001 = OccupationConfigParser.getItemsByOccupationId(4001)
	local items4002 = OccupationConfigParser.getItemsByOccupationId(4002)
	
	print("职业4001物品数量:", #items4001)
	print("职业4002物品数量:", #items4002)
	
	-- 打印物品详情
	OccupationConfigParser.printOccupationItems(4001)
	OccupationConfigParser.printOccupationItems(4002)
	
	-- 测试格式验证
	local validFormat = OccupationConfigParser.validateCarryingItemFormat("2_7_10026;50_6_10029")
	local invalidFormat = OccupationConfigParser.validateCarryingItemFormat("2_7;50_6_10029")
	
	print("有效格式验证:", validFormat)
	print("无效格式验证:", invalidFormat)
	
	print("=== OccupationConfigParser 测试完成 ===\n")
end

-- 测试PlayerItemManager
function TestOccupationSystem.testPlayerItemManager()
	print("=== 测试 PlayerItemManager ===")
	
	-- 初始化PlayerItemManager
	PlayerItemManager.InitServer()
	
	-- 创建模拟玩家数据
	local mockPlayer1 = {
		UserId = 12345,
		Name = "TestPlayer1"
	}
	
	local mockPlayer2 = {
		UserId = 67890,
		Name = "TestPlayer2"
	}
	
	-- 测试设置玩家物品数据
	local success1 = PlayerItemManager.setPlayerOccupationItems(mockPlayer1, 4001)
	local success2 = PlayerItemManager.setPlayerOccupationItems(mockPlayer2, 4002)
	
	print("设置玩家1物品数据:", success1 and "成功" or "失败")
	print("设置玩家2物品数据:", success2 and "成功" or "失败")
	
	-- 测试获取玩家物品数据
	local player1Data = PlayerItemManager.getPlayerItemData(12345)
	local player2Data = PlayerItemManager.getPlayerItemData(67890)
	
	print("获取玩家1数据:", player1Data and "成功" or "失败")
	print("获取玩家2数据:", player2Data and "成功" or "失败")
	
	-- 打印所有玩家物品数据
	PlayerItemManager.printAllPlayerItemData()
	
	-- 测试传送数据格式
	local teleportData = PlayerItemManager.getTeleportItemData()
	print("传送数据格式:")
	for userId, data in pairs(teleportData) do
		print("  玩家ID " .. userId .. ": " .. data.playerName .. ", 职业ID " .. data.occupationId .. ", 物品数量 " .. #data.items)
	end
	
	-- 测试数据验证
	local valid1, msg1 = PlayerItemManager.validatePlayerItemData(12345)
	local valid2, msg2 = PlayerItemManager.validatePlayerItemData(67890)
	
	print("玩家1数据验证:", valid1 and "通过" or "失败", msg1)
	print("玩家2数据验证:", valid2 and "通过" or "失败", msg2)
	
	print("=== PlayerItemManager 测试完成 ===\n")
end

-- 测试完整流程
function TestOccupationSystem.testCompleteFlow()
	print("=== 测试完整流程 ===")
	
	-- 模拟玩家
	local mockPlayer = {
		UserId = 11111,
		Name = "FlowTestPlayer",
		Character = nil -- 简化测试，不创建实际角色
	}
	
	-- 步骤1: 解析职业配置
	print("步骤1: 解析职业配置")
	local occupationConfig = OccupationConfigParser.getOccupationConfig(4001)
	if occupationConfig then
		print("  职业配置获取成功")
	else
		print("  职业配置获取失败")
		return
	end
	
	-- 步骤2: 设置玩家物品数据
	print("步骤2: 设置玩家物品数据")
	local itemSuccess = PlayerItemManager.setPlayerOccupationItems(mockPlayer, 4001)
	if itemSuccess then
		print("  玩家物品数据设置成功")
	else
		print("  玩家物品数据设置失败")
		return
	end
	
	-- 步骤3: 验证数据存储
	print("步骤3: 验证数据存储")
	local playerData = PlayerItemManager.getPlayerItemData(11111)
	if playerData then
		print("  数据存储验证成功")
		print("    玩家名字:", playerData.playerName)
		print("    职业ID:", playerData.occupationId)
		print("    物品数量:", #playerData.items)
	else
		print("  数据存储验证失败")
		return
	end
	
	-- 步骤4: 模拟传送数据准备
	print("步骤4: 模拟传送数据准备")
	local teleportData = PlayerItemManager.getTeleportItemData()
	if teleportData[11111] then
		print("  传送数据准备成功")
		print("    传送数据包含玩家:", teleportData[11111].playerName)
		print("    职业ID:", teleportData[11111].occupationId)
		print("    物品数量:", #teleportData[11111].items)
	else
		print("  传送数据准备失败")
		return
	end
	
	print("=== 完整流程测试成功 ===\n")
end

-- 运行所有测试
function TestOccupationSystem.runAllTests()
	print("开始运行职业系统测试...")
	print("=" .. string.rep("=", 50))
	
	TestOccupationSystem.testOccupationConfigParser()
	TestOccupationSystem.testPlayerItemManager()
	TestOccupationSystem.testCompleteFlow()
	
	print("=" .. string.rep("=", 50))
	print("所有测试完成！")
end

-- 清理测试数据
function TestOccupationSystem.cleanup()
	print("清理测试数据...")
	PlayerItemManager.clearPlayerItemData(12345)
	PlayerItemManager.clearPlayerItemData(67890)
	PlayerItemManager.clearPlayerItemData(11111)
	print("测试数据清理完成")
end

return TestOccupationSystem
