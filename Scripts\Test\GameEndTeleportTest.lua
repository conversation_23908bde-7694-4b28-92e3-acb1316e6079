--!strict
-- 游戏结束传送功能测试脚本
-- 用于测试全员濒死后10秒传送回初始场景的功能

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local GameEndTeleportService = require(ReplicatedStorage.Scripts.Server.Services.GameEndTeleportService)
local PlayerDownedService = require(ReplicatedStorage.Scripts.Server.Services.PlayerDownedService)

local GameEndTeleportTest = {}

-- 测试配置
local TEST_INITIAL_PLACE_ID = 89908111666289 -- 测试用的初始场景ID

-- 模拟所有玩家濒死状态
function GameEndTeleportTest.simulateAllPlayersDowned()
	print("=== 模拟所有玩家濒死状态 ===")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("没有玩家可用于测试")
		return false
	end
	
	print("当前在线玩家数量:", #allPlayers)
	
	-- 模拟所有玩家进入濒死状态
	for _, player in ipairs(allPlayers) do
		if player.Character and player.Character:FindFirstChild("Humanoid") then
			print("模拟玩家", player.Name, "进入濒死状态")
			
			-- 直接调用濒死处理函数
			PlayerDownedService:HandlePlayerDowning(player, player.Character)
		else
			warn("玩家", player.Name, "没有有效的角色")
		end
	end
	
	print("=== 所有玩家濒死状态模拟完成 ===")
	return true
end

-- 测试倒计时功能
function GameEndTeleportTest.testCountdownSystem()
	print("=== 测试倒计时系统 ===")
	
	-- 检查倒计时状态
	local status = GameEndTeleportService:GetCountdownStatus()
	print("倒计时状态:")
	print("  激活:", status.active)
	print("  剩余时间:", status.remaining)
	print("  总时间:", status.total)
	print("  目标场景ID:", status.targetPlaceId or "未设置")
	
	if status.active then
		print("✅ 倒计时系统正在运行")
		return true
	else
		print("⚠️ 倒计时系统未激活")
		return false
	end
end

-- 测试手动传送功能
function GameEndTeleportTest.testManualTeleport()
	print("=== 测试手动传送功能 ===")
	
	local allPlayers = Players:GetPlayers()
	if #allPlayers == 0 then
		warn("没有玩家可用于测试")
		return false
	end
	
	print("即将手动触发传送，目标玩家数量:", #allPlayers)
	
	-- 等待确认
	wait(2)
	
	-- 执行手动传送
	GameEndTeleportService:ManualTeleportToInitial()
	
	print("✅ 手动传送已触发")
	return true
end

-- 测试倒计时停止功能
function GameEndTeleportTest.testCountdownStop()
	print("=== 测试倒计时停止功能 ===")
	
	-- 首先确保有倒计时在运行
	local status = GameEndTeleportService:GetCountdownStatus()
	if not status.active then
		print("没有活跃的倒计时，先启动一个")
		GameEndTeleportService:StartAllDownedCountdown()
		wait(1)
	end
	
	-- 模拟玩家复活
	local testPlayer = Players:GetPlayers()[1]
	if testPlayer then
		print("模拟玩家", testPlayer.Name, "复活")
		PlayerDownedService:RevivePlayer(testPlayer)
		
		-- 检查倒计时是否停止
		wait(1)
		local newStatus = GameEndTeleportService:GetCountdownStatus()
		if not newStatus.active then
			print("✅ 倒计时已成功停止")
			return true
		else
			print("❌ 倒计时未停止")
			return false
		end
	else
		warn("没有玩家可用于测试")
		return false
	end
end

-- 测试初始场景ID检测
function GameEndTeleportTest.testInitialPlaceDetection()
	print("=== 测试初始场景ID检测 ===")
	
	-- 模拟传送数据
	local TeleportDataManager = require(ReplicatedStorage.Scripts.Server.Services.TeleportDataManager)
	local testPlayer = Players:GetPlayers()[1]
	
	if not testPlayer then
		warn("没有玩家可用于测试")
		return false
	end
	
	-- 创建测试传送数据
	local testTeleportData = {
		sourcePlace = TEST_INITIAL_PLACE_ID,
		timestamp = os.time(),
		playerItemData = {},
		professionPlayer = {},
		professionState = {}
	}
	
	-- 设置测试数据
	local success = TeleportDataManager:SetTestTeleportData(testPlayer, testTeleportData)
	if success then
		print("✅ 测试传送数据设置成功")
		
		-- 重新检测初始场景ID
		GameEndTeleportService:DetectInitialPlaceId()
		
		print("✅ 初始场景ID检测完成")
		return true
	else
		warn("❌ 测试传送数据设置失败")
		return false
	end
end

-- 完整的功能测试流程
function GameEndTeleportTest.runFullTest()
	print("🚀 开始完整的游戏结束传送功能测试")
	print("=" * 50)
	
	local results = {}
	
	-- 测试1: 初始场景ID检测
	print("\n📋 测试1: 初始场景ID检测")
	results.placeDetection = GameEndTeleportTest.testInitialPlaceDetection()
	
	-- 测试2: 模拟所有玩家濒死
	print("\n📋 测试2: 模拟所有玩家濒死")
	results.allPlayersDowned = GameEndTeleportTest.simulateAllPlayersDowned()
	
	-- 等待倒计时启动
	wait(2)
	
	-- 测试3: 倒计时系统
	print("\n📋 测试3: 倒计时系统")
	results.countdownSystem = GameEndTeleportTest.testCountdownSystem()
	
	-- 测试4: 倒计时停止功能
	print("\n📋 测试4: 倒计时停止功能")
	results.countdownStop = GameEndTeleportTest.testCountdownStop()
	
	-- 输出测试结果
	print("\n" .. "=" * 50)
	print("🏁 测试结果总结:")
	print("  初始场景ID检测:", results.placeDetection and "✅ 通过" or "❌ 失败")
	print("  所有玩家濒死:", results.allPlayersDowned and "✅ 通过" or "❌ 失败")
	print("  倒计时系统:", results.countdownSystem and "✅ 通过" or "❌ 失败")
	print("  倒计时停止:", results.countdownStop and "✅ 通过" or "❌ 失败")
	
	local allPassed = results.placeDetection and results.allPlayersDowned and 
					  results.countdownSystem and results.countdownStop
	
	if allPassed then
		print("\n🎉 所有测试通过！游戏结束传送功能正常工作")
	else
		print("\n⚠️ 部分测试失败，请检查相关功能")
	end
	
	print("=" * 50)
	return allPassed
end

-- 快速测试（仅测试核心功能）
function GameEndTeleportTest.runQuickTest()
	print("⚡ 快速测试：模拟全员濒死并观察倒计时")
	
	-- 模拟所有玩家濒死
	local success = GameEndTeleportTest.simulateAllPlayersDowned()
	if success then
		print("✅ 快速测试启动成功，请观察倒计时UI")
		print("💡 倒计时将在10秒后自动传送所有玩家")
	else
		warn("❌ 快速测试启动失败")
	end
	
	return success
end

-- 紧急停止测试
function GameEndTeleportTest.emergencyStop()
	print("🛑 紧急停止所有测试")
	
	-- 停止倒计时
	GameEndTeleportService:StopAllDownedCountdown()
	
	-- 复活所有玩家
	for _, player in ipairs(Players:GetPlayers()) do
		if PlayerDownedService:IsPlayerDowned(player) then
			PlayerDownedService:RevivePlayer(player)
			print("复活玩家:", player.Name)
		end
	end
	
	print("✅ 紧急停止完成")
end

-- 显示测试帮助信息
function GameEndTeleportTest.showHelp()
	print("🔧 游戏结束传送功能测试帮助")
	print("可用的测试函数:")
	print("  runFullTest() - 运行完整测试套件")
	print("  runQuickTest() - 快速测试核心功能")
	print("  simulateAllPlayersDowned() - 模拟所有玩家濒死")
	print("  testCountdownSystem() - 测试倒计时系统")
	print("  testManualTeleport() - 测试手动传送")
	print("  emergencyStop() - 紧急停止所有测试")
	print("  showHelp() - 显示此帮助信息")
end

return GameEndTeleportTest
