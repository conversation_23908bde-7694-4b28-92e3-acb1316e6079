-- 增强的游戏场景数据接收脚本
-- 集成新手盒子系统，实现传输数据的实际应用

local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ProtocolManager 	= require(ReplicatedStorage.Scripts.Share.Manager.ProtocolManager)
local ProfessionDataModule = require(ReplicatedStorage.Scripts.Client.Controller.ProfessionDataModule)
local player = Players.LocalPlayer

-- 智能等待系统就绪
local function waitForSystemReady()
	print("🔄 等待系统就绪...")

	-- 等待ReplicatedStorage中的关键服务
	local maxWaitTime = 10 -- 最大等待10秒
	local startTime = tick()

	while tick() - startTime < maxWaitTime do
		-- 检查RemoteEvent是否存在
		local remotesFolder = ReplicatedStorage:FindFirstChild("Remotes")
		if remotesFolder and remotesFolder:FindFirstChild("TeleportDataReceived") then
			print("✅ 系统就绪，RemoteEvent已可用")
			return true
		end

		wait(0.5) -- 每0.5秒检查一次
	end

	warn("⚠️ 系统等待超时，继续执行...")
	return false
end

-- 等待系统就绪
waitForSystemReady()

print("🚀 开始处理传输数据...")

-- 获取传输数据
local data = TeleportService:GetLocalPlayerTeleportData()

if data then
	print("✅ 接收到传送数据:")
	print("📋 基本信息:")
	print("  职业:", data.job or "未知")
	print("  描述:", data.str or "未知")
	print("  源场景:", data.sourcePlace or "未知")
	print("  传输时间:", data.timestamp and os.date("%Y-%m-%d %H:%M:%S", data.timestamp) or "未知")
	-- 处理职业数据
	if data.professionPlayer then
		local userId = tostring(player.UserId) -- 将数字类型转换为字符串类型
		print("玩家Id", userId)

		for key, value in pairs(data.professionPlayer) do
			print("接收Id", key)

			if userId == key then
				print("玩家Id匹配更换模型")
				local profession = value
				print("接收职业",profession)
				if profession ~= "" then
					ProfessionDataModule:SetUserIdData(key)
					ProfessionDataModule:SetNameData(profession)
					print("已设置职业:",profession)
				else
					warn("职业数据不完整，缺少profession")
				end
				break
			end
		end
	else
		print("\n⚠️  未接收到职业数据")
	end
	if data.professionState then
		local userId = tostring(player.UserId) -- 将数字类型转换为字符串类型
		print("玩家Id", userId)

		for key, value in pairs(data.professionState) do
			print("接收Id", key)

			if userId == key then
				print("玩家Id匹配更换数据")
				local id = value
				print("接收职业数据",id)
				if id ~= 0 then
					ProfessionDataModule:SetStateData(id)
					print("已设置职业数据:",id)
				else
					warn("职业数据不完整，缺少id")
				end
				-- 找到匹配项后退出循环
				break
			end
		end
	else
		print("\n⚠️  未接收到职业数据")
	end
	-- 处理物品数据（支持新的playerItemData结构）
	local playerItems = nil
	local itemsSource = "未知"

	-- 优先检查新的playerItemData结构
	if data.playerItemData then
		local userId = tostring(player.UserId)
		print("🔍 检查新格式playerItemData，玩家ID:", userId)

		for playerId, playerData in pairs(data.playerItemData) do
			print("  - 找到玩家数据，ID:", playerId)
			if playerId == userId then
				playerItems = playerData.items
				itemsSource = "playerItemData (职业ID: " .. (playerData.occupationId or "未知") .. ")"
				print("✅ 匹配成功！使用玩家专属物品数据")
				break
			end
		end
	end

	-- 如果新格式没有数据，尝试旧格式兼容
	if not playerItems and data.items then
		playerItems = data.items
		itemsSource = "传统items格式"
		print("📦 使用传统items格式数据")
	end

	-- 打印物品数据
	if playerItems and #playerItems > 0 then
		print("\n📦 携带的物品 (" .. #playerItems .. " 个) - 数据源: " .. itemsSource)
		for i, item in ipairs(playerItems) do
			local id = item.id or "未知物品"
			local quantity = item.quantity or 0
			local itemType = item.itemType or "未知类型"

			-- 根据itemType显示物品类型名称
			local typeName = "未知"
			if itemType == 9 then typeName = "近战武器"
			elseif itemType == 10 then typeName = "远程武器"
			elseif itemType == 7 then typeName = "消耗品"
			elseif itemType == 11 then typeName = "装备"
			elseif itemType == 6 then typeName = "弹药"
			end

			print(string.format("  %d. ID:%s (%s): %d 个", i, id, typeName, quantity))
		end

		print("\n🎯 传输数据已准备就绪，新手盒子系统将自动使用这些数据")
		print("💡 当角色生成时，新手盒子将包含上述物品而不是默认配置")
	else
		print("\n⚠️  未携带物品数据，将使用默认新手盒子配置")
		print("🔍 检查结果:")
		print("  - playerItemData存在:", data.playerItemData ~= nil)
		print("  - 传统items存在:", data.items ~= nil)
	end

	-- 发送完整传输数据给服务器端
	local success, remoteEvent = pcall(function()
		return ReplicatedStorage:WaitForChild("Remotes"):WaitForChild("TeleportDataReceived")
	end)

	if success and remoteEvent then
		-- 发送完整的传输数据给服务端
		remoteEvent:FireServer(data)
		print("📡 已发送完整传输数据给服务器端")
	else
		warn("⚠️  无法找到TeleportDataReceived RemoteEvent")
	end

else
	print("❌ 未接收到传送数据")
	print("💡 将使用默认新手盒子配置")

	-- 通知服务器端没有传输数据
	local success, remoteEvent = pcall(function()
		return ReplicatedStorage:WaitForChild("Remotes"):WaitForChild("TeleportDataReceived")
	end)

	if success and remoteEvent then
		-- 发送nil表示没有传输数据
		remoteEvent:FireServer(nil)
		print("📡 已通知服务器端没有传输数据")
	else
		warn("⚠️  无法找到TeleportDataReceived RemoteEvent")
	end
end

print("🏁 传输数据处理完成")