--!strict
export type ItemConfig = {
  Id: number,
  Chinese: string,
  Name: string,
  Icon: string,
  ItemModelName: string,
  ItemQuality: number,
  ItemType: number,
  SaleType: number,
  ObtainNumber: number,
  PurchaseType: number,
  ConsumptionQuantity: number,
  CombustionType: string,
  CombustionValue: number
}

local ItemConfig: {ItemConfig} = {
	{Id=10001, Chinese="煤炭", Name="coal", Icon="0", ItemModelName="LineSegment", ItemQuality=1, ItemType=0, SaleType=1, ObtainNumber=5, PurchaseType=1, ConsumptionQuantity=20, CombustionType="1", CombustionValue=75},
  {Id=10002, Chinese="铁板", Name="iron plate", Icon="0", ItemQuality=1, ItemType=4, SaleType=1, ObtainNumber=10, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10003, Chinese="银块", Name="silver bullion", ItemQuality=2, ItemType=1, SaleType=1, ObtainNumber=25, PurchaseType=0, CombustionType="0"},
  {Id=10004, Chinese="金块", Name="gold bullion", Icon="0", ItemQuality=3, ItemType=1, SaleType=1, ObtainNumber=50, PurchaseType=0, CombustionType="0"},
  {Id=10005, Chinese="木画像1", Icon="0", ItemQuality=1, ItemType=0,3, SaleType=1, ObtainNumber=5, PurchaseType=1, ConsumptionQuantity=10, CombustionType="1", CombustionValue=20},
  {Id=10006, Chinese="木画像2", ItemQuality=1, ItemType=0,3, SaleType=1, ObtainNumber=5, PurchaseType=1, ConsumptionQuantity=10, CombustionType="1", CombustionValue=20},
  {Id=10007, Chinese="木画像3", ItemQuality=1, ItemType=0,3, SaleType=1, ObtainNumber=5, PurchaseType=1, ConsumptionQuantity=10, CombustionType="1", CombustionValue=20},
  {Id=10008, Chinese="银画像1", ItemQuality=2, ItemType=1,3, SaleType=1, ObtainNumber=20, PurchaseType=0, CombustionType="0"},
  {Id=10009, Chinese="银画像2", ItemQuality=2, ItemType=1,3, SaleType=1, ObtainNumber=20, PurchaseType=0, CombustionType="0"},
  {Id=10010, Chinese="银画像3", ItemQuality=2, ItemType=1,3, SaleType=1, ObtainNumber=20, PurchaseType=0, CombustionType="0"},
  {Id=10011, Chinese="金画像1", ItemQuality=3, ItemType=1,3, SaleType=1, ObtainNumber=40, PurchaseType=0, CombustionType="0"},
  {Id=10012, Chinese="金画像2", ItemQuality=3, ItemType=1,3, SaleType=1, ObtainNumber=40, PurchaseType=0, CombustionType="0"},
  {Id=10013, Chinese="金画像3", ItemQuality=3, ItemType=1,3, SaleType=1, ObtainNumber=40, PurchaseType=0, CombustionType="0"},
  {Id=10014, Chinese="血晶画像1", ItemQuality=4, ItemType=1,3, SaleType=1, ObtainNumber=80, PurchaseType=0, CombustionType="0"},
  {Id=10015, Chinese="血晶画像2", Icon="0", ItemQuality=4, ItemType=1,3, SaleType=1, ObtainNumber=80, PurchaseType=0, CombustionType="0"},
  {Id=10016, Chinese="血晶画像3", ItemQuality=4, ItemType=1,3, SaleType=1, ObtainNumber=80, PurchaseType=0, CombustionType="0"},
  {Id=10017, Chinese="钻石", ItemQuality=4, ItemType=1, SaleType=1, ObtainNumber=100, PurchaseType=0, CombustionType="0"},
	{Id=10018, Chinese="血晶", Name="haemin",Icon="0",ItemModelName = "Coin", ItemQuality=4, ItemType=2, SaleType=0, PurchaseType=0, CombustionType="0"},
  {Id=10019, Chinese="木椅", Name="chair", Icon="0", ItemQuality=1, ItemType=0,3, SaleType=1, ObtainNumber=5, PurchaseType=1, ConsumptionQuantity=10, CombustionType="1", CombustionValue=20},
  {Id=10020, Chinese="银椅子", ItemQuality=2, ItemType=1,3, SaleType=1, ObtainNumber=20, PurchaseType=0, CombustionType="0"},
  {Id=10021, Chinese="金椅子", ItemQuality=3, ItemType=1,3, SaleType=1, ObtainNumber=40, PurchaseType=0, CombustionType="0"},
  {Id=10022, Chinese="血晶椅子", ItemQuality=4, ItemType=1,3, SaleType=1, ObtainNumber=80, PurchaseType=0, CombustionType="0"},
  {Id=10023, Chinese="火把", Name="FireBall", ItemQuality=1, ItemType=1,5, SaleType=1, ObtainNumber=5, PurchaseType=1, ConsumptionQuantity=20, CombustionType="0"},
  {Id=10024, Chinese="提灯", Name="hand lantern", ItemQuality=2, ItemType=1,5, SaleType=1, ObtainNumber=10, PurchaseType=1, ConsumptionQuantity=40, CombustionType="0"},
  {Id=10025, Chinese="炸药", ItemQuality=3, ItemType=8, SaleType=1, ObtainNumber=20, PurchaseType=1, ConsumptionQuantity=40, CombustionType="0"},
  {Id=10026, Chinese="绷带", Name="Bandage",Icon="rbxassetid://98922418380300",ItemModelName="Bandage", ItemQuality=1, ItemType=7, SaleType=0, PurchaseType=1, ConsumptionQuantity=20, CombustionType="0"},
  {Id=10027, Chinese="血瓶", ItemQuality=2, ItemType=7, SaleType=0, PurchaseType=1, ConsumptionQuantity=35, CombustionType="0"},
  {Id=10028, Chinese="加速药水", ItemQuality=2, ItemType=7, SaleType=0, PurchaseType=1, ConsumptionQuantity=40, CombustionType="0"},
	{Id=10029, Chinese="手枪子弹", Name="Bullet_01", ItemModelName="Bullet_01",ItemQuality=1, ItemType=6, SaleType=0, PurchaseType=1, ConsumptionQuantity=20, CombustionType="0"},
  {Id=10030, Chinese="步枪子弹", Name="Bullet_02", ItemQuality=1, ItemType=6, SaleType=0, PurchaseType=1, ConsumptionQuantity=25, CombustionType="0"},
  {Id=10031, Chinese="木棒", Name="FairySword", Icon="rbxassetid://8873427418037", ItemModelName="FairySword", ItemQuality=1, ItemType=9, SaleType=0, PurchaseType=0, CombustionType="0"},
  {Id=10032, Chinese="日轮刀", Name="GreenSteel", Icon="rbxassetid://113900514027324", ItemModelName="GreenSteel", ItemQuality=1, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10033, Chinese="锯齿双刀", ItemQuality=1, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10034, Chinese="炎之呼吸·炼狱刀", ItemQuality=2, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10035, Chinese="雷之呼吸·鸣雷刀", ItemQuality=2, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10036, Chinese="虫之呼吸·蝶陨毒刺", ItemQuality=2, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10037, Chinese="恋之呼吸·蜜雪软刃", ItemQuality=2, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10038, Chinese="岩之呼吸·流星锤斧", ItemQuality=2, ItemType=9, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10039, Chinese="铁头盔", Name="Black_Head", Icon="rbxassetid://81845394476205", ItemModelName="Black_Head", ItemQuality=1, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10040, Chinese="火男面具", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10041, Chinese="宝石头盔", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10042, Chinese="铁盔甲", ItemQuality=1, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10043, Chinese="宝石盔甲", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10044, Chinese="蝴蝶翅膀", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10045, Chinese="铁护臂", ItemQuality=1, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10046, Chinese="宝石护臂", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10047, Chinese="烈焰护臂", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10048, Chinese="铁护腿", ItemQuality=1, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10049, Chinese="宝石护腿", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10050, Chinese="跑鞋", ItemQuality=2, ItemType=11, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10051, Chinese="手枪", Name="HyperlaserGun", Icon="rbxassetid://79132584757164", ItemModelName="HyperlaserGun", ItemQuality=1, ItemType=10, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10052, Chinese="冲锋枪", Name="MP5K", Icon="rbxassetid://96616508284247", ItemModelName="MP5K", ItemQuality=2, ItemType=10, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"},
  {Id=10053, Chinese="猎枪", ItemQuality=1, ItemType=10, SaleType=0, PurchaseType=1, ConsumptionQuantity=50, CombustionType="0"},
  {Id=10054, Chinese="步枪", ItemQuality=2, ItemType=10, SaleType=0, PurchaseType=1, ConsumptionQuantity=100, CombustionType="0"}
}

return ItemConfig
