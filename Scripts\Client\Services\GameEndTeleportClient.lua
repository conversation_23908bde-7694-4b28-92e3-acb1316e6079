--!strict
-- 游戏结束传送客户端服务
-- 负责显示倒计时UI和处理传送相关的客户端逻辑

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local GameEndTeleportClient = {}

-- UI引用
local countdownUI = nil
local countdownFrame = nil
local countdownText = nil
local messageText = nil
local progressBar = nil
local backgroundFrame = nil

-- 状态管理
local isCountdownActive = false
local countdownStartTime = 0
local totalCountdownTime = 10

-- 初始化客户端服务
function GameEndTeleportClient:Initialize()
	print("GameEndTeleportClient 初始化开始")
	
	-- 注册服务端事件
	self:RegisterEvents()
	
	print("GameEndTeleportClient 初始化完成")
end

-- 注册服务端事件
function GameEndTeleportClient:RegisterEvents()
	-- 监听所有玩家濒死事件
	NotifyService.RegisterClientEvent("AllPlayersDowned", function(data)
		self:OnAllPlayersDowned(data)
	end)
	
	-- 监听倒计时开始事件
	NotifyService.RegisterClientEvent("AllDownedCountdownStart", function(data)
		self:OnCountdownStart(data)
	end)
	
	-- 监听倒计时更新事件
	NotifyService.RegisterClientEvent("AllDownedCountdownUpdate", function(data)
		self:OnCountdownUpdate(data)
	end)
	
	-- 监听倒计时停止事件
	NotifyService.RegisterClientEvent("AllDownedCountdownStop", function(data)
		self:OnCountdownStop(data)
	end)
	
	-- 监听传送开始事件
	NotifyService.RegisterClientEvent("TeleportToInitialStart", function(data)
		self:OnTeleportStart(data)
	end)
	
	-- 监听传送失败事件
	NotifyService.RegisterClientEvent("TeleportToInitialFailed", function(data)
		self:OnTeleportFailed(data)
	end)
end

-- 处理所有玩家濒死事件
function GameEndTeleportClient:OnAllPlayersDowned(data)
	print("收到所有玩家濒死通知:", data)
	
	-- 显示初始提示信息
	self:ShowInitialMessage(data.message or "所有玩家濒死，即将传送回初始场景")
end

-- 处理倒计时开始事件
function GameEndTeleportClient:OnCountdownStart(data)
	print("开始传送倒计时:", data)
	
	isCountdownActive = true
	countdownStartTime = data.startTime or tick()
	totalCountdownTime = data.countdownTime or 10
	
	-- 创建倒计时UI
	self:CreateCountdownUI(data)
end

-- 处理倒计时更新事件
function GameEndTeleportClient:OnCountdownUpdate(data)
	if not isCountdownActive or not countdownText then
		return
	end
	
	local remainingTime = data.remainingTime or 0
	
	-- 更新倒计时文本
	countdownText.Text = tostring(remainingTime)
	
	-- 更新进度条
	if progressBar then
		local progress = 1 - (remainingTime / totalCountdownTime)
		progressBar.Size = UDim2.new(progress, 0, 1, 0)
	end
	
	-- 添加视觉效果
	self:AddCountdownEffects(remainingTime)
end

-- 处理倒计时停止事件
function GameEndTeleportClient:OnCountdownStop(data)
	print("倒计时停止:", data.reason or "未知原因")
	
	isCountdownActive = false
	
	-- 显示停止原因
	if data.reason == "playerRevived" then
		self:ShowMessage("有玩家复活，传送已取消", 3)
	end
	
	-- 移除倒计时UI
	self:RemoveCountdownUI()
end

-- 处理传送开始事件
function GameEndTeleportClient:OnTeleportStart(data)
	print("开始传送回初始场景:", data)
	
	-- 更新UI显示传送状态
	if messageText then
		messageText.Text = "正在传送回初始场景..."
	end
	
	-- 添加传送效果
	self:AddTeleportEffects()
end

-- 处理传送失败事件
function GameEndTeleportClient:OnTeleportFailed(data)
	warn("传送失败:", data.error)
	
	-- 显示错误信息
	self:ShowMessage("传送失败: " .. (data.error or "未知错误"), 5)
	
	-- 移除UI
	self:RemoveCountdownUI()
end

-- 创建倒计时UI
function GameEndTeleportClient:CreateCountdownUI(data)
	local player = Players.LocalPlayer
	if not player then return end
	
	local playerGui = player:FindFirstChild("PlayerGui")
	if not playerGui then return end
	
	-- 移除已存在的UI
	self:RemoveCountdownUI()
	
	-- 创建主UI
	countdownUI = Instance.new("ScreenGui")
	countdownUI.Name = "GameEndCountdownUI"
	countdownUI.ResetOnSpawn = false
	countdownUI.Parent = playerGui
	
	-- 创建背景
	backgroundFrame = Instance.new("Frame")
	backgroundFrame.Name = "Background"
	backgroundFrame.Size = UDim2.new(1, 0, 1, 0)
	backgroundFrame.BackgroundColor3 = Color3.new(0, 0, 0)
	backgroundFrame.BackgroundTransparency = 0.3
	backgroundFrame.BorderSizePixel = 0
	backgroundFrame.Parent = countdownUI
	
	-- 创建主框架
	countdownFrame = Instance.new("Frame")
	countdownFrame.Name = "CountdownFrame"
	countdownFrame.Size = UDim2.new(0, 400, 0, 200)
	countdownFrame.Position = UDim2.new(0.5, -200, 0.5, -100)
	countdownFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	countdownFrame.BackgroundTransparency = 0.2
	countdownFrame.BorderSizePixel = 2
	countdownFrame.BorderColor3 = Color3.new(1, 0.5, 0)
	countdownFrame.Parent = countdownUI
	
	-- 添加圆角
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = countdownFrame
	
	-- 创建标题
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -20, 0, 40)
	titleText.Position = UDim2.new(0, 10, 0, 10)
	titleText.BackgroundTransparency = 1
	titleText.Text = "传送倒计时"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextScaled = true
	titleText.Font = Enum.Font.SourceSansBold
	titleText.Parent = countdownFrame
	
	-- 创建消息文本
	messageText = Instance.new("TextLabel")
	messageText.Name = "MessageText"
	messageText.Size = UDim2.new(1, -20, 0, 30)
	messageText.Position = UDim2.new(0, 10, 0, 50)
	messageText.BackgroundTransparency = 1
	messageText.Text = data.message or "所有玩家濒死，即将传送回初始场景"
	messageText.TextColor3 = Color3.new(0.9, 0.9, 0.9)
	messageText.TextScaled = true
	messageText.Font = Enum.Font.SourceSans
	messageText.Parent = countdownFrame
	
	-- 创建倒计时文本
	countdownText = Instance.new("TextLabel")
	countdownText.Name = "CountdownText"
	countdownText.Size = UDim2.new(1, -20, 0, 60)
	countdownText.Position = UDim2.new(0, 10, 0, 90)
	countdownText.BackgroundTransparency = 1
	countdownText.Text = tostring(totalCountdownTime)
	countdownText.TextColor3 = Color3.new(1, 0.5, 0)
	countdownText.TextScaled = true
	countdownText.Font = Enum.Font.SourceSansBold
	countdownText.Parent = countdownFrame
	
	-- 创建进度条背景
	local progressBG = Instance.new("Frame")
	progressBG.Name = "ProgressBackground"
	progressBG.Size = UDim2.new(1, -20, 0, 10)
	progressBG.Position = UDim2.new(0, 10, 0, 170)
	progressBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	progressBG.BorderSizePixel = 0
	progressBG.Parent = countdownFrame
	
	-- 创建进度条
	progressBar = Instance.new("Frame")
	progressBar.Name = "ProgressBar"
	progressBar.Size = UDim2.new(0, 0, 1, 0)
	progressBar.Position = UDim2.new(0, 0, 0, 0)
	progressBar.BackgroundColor3 = Color3.new(1, 0.5, 0)
	progressBar.BorderSizePixel = 0
	progressBar.Parent = progressBG
	
	-- 添加进入动画
	countdownFrame.Position = UDim2.new(0.5, -200, 1.5, 0)
	local enterTween = TweenService:Create(countdownFrame, 
		TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{Position = UDim2.new(0.5, -200, 0.5, -100)}
	)
	enterTween:Play()
end

-- 移除倒计时UI
function GameEndTeleportClient:RemoveCountdownUI()
	if countdownUI then
		-- 添加退出动画
		if countdownFrame then
			local exitTween = TweenService:Create(countdownFrame,
				TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
				{Position = UDim2.new(0.5, -200, -0.5, 0)}
			)
			exitTween:Play()
			
			exitTween.Completed:Connect(function()
				countdownUI:Destroy()
			end)
		else
			countdownUI:Destroy()
		end
		
		countdownUI = nil
		countdownFrame = nil
		countdownText = nil
		messageText = nil
		progressBar = nil
		backgroundFrame = nil
	end
end

-- 添加倒计时视觉效果
function GameEndTeleportClient:AddCountdownEffects(remainingTime)
	if not countdownText then return end
	
	-- 最后5秒时添加闪烁效果
	if remainingTime <= 5 then
		local flashTween = TweenService:Create(countdownText,
			TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
			{TextColor3 = Color3.new(1, 0, 0)}
		)
		flashTween:Play()
		
		-- 添加缩放效果
		local scaleTween = TweenService:Create(countdownText,
			TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{Size = UDim2.new(1.2, -20, 0, 72)}
		)
		scaleTween:Play()
		
		scaleTween.Completed:Connect(function()
			local resetTween = TweenService:Create(countdownText,
				TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
				{Size = UDim2.new(1, -20, 0, 60)}
			)
			resetTween:Play()
		end)
	end
end

-- 添加传送效果
function GameEndTeleportClient:AddTeleportEffects()
	if not backgroundFrame then return end
	
	-- 创建传送光效
	local teleportEffect = TweenService:Create(backgroundFrame,
		TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
		{BackgroundColor3 = Color3.new(1, 1, 1), BackgroundTransparency = 0}
	)
	teleportEffect:Play()
end

-- 显示初始消息
function GameEndTeleportClient:ShowInitialMessage(message)
	-- 可以在这里添加简单的消息提示
	print("📢 " .. message)
end

-- 显示临时消息
function GameEndTeleportClient:ShowMessage(message, duration)
	print("💬 " .. message)
	-- 这里可以添加临时消息UI的实现
end

return GameEndTeleportClient
