local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TeleportService = game:GetService("TeleportService")
local ProfessionManager = require(ReplicatedStorage.Scripts.Server.Manager.ProfessionManager)
local PlayerItemManager = require(ReplicatedStorage.Scripts.Server.Manager.PlayerItemManager)
local TARGET_PLACE_ID = 89908111666289
local TeleportManager = {}

-- 安全获取玩家函数
local function getFirstPlayer()
	local players = Players:GetPlayers()
	if #players > 0 then
		return players[1]
	else
		warn("当前没有玩家在线")
		return nil
	end
end

-- 主函数
function TeleportManager:teleportPlayers(players)
	if #players == 0 then
		warn("没有玩家可传送")
		return
	end

	-- 获取玩家物品数据
	local playerItemData = PlayerItemManager.getTeleportItemData()

	-- 打印传送数据用于检查
	print("=== 传送数据检查 ===")
	print("传送玩家数量:", #players)
	print("目标场景ID:", TARGET_PLACE_ID)
	print("源场景ID:", game.PlaceId)
	print("时间戳:", os.date("%Y-%m-%d %H:%M:%S", os.time()))

	-- 打印职业数据
	print("--- 职业数据 ---")
	for userId, profession in pairs(ProfessionManager.playerProfession) do
		local professionState = ProfessionManager.playerProfessionState[userId]
		print("玩家ID " .. userId .. ": 职业=" .. profession .. ", 状态ID=" .. (professionState or "无"))
	end

	-- 打印物品数据
	print("--- 物品数据 ---")
	if next(playerItemData) == nil then
		print("没有玩家物品数据")
	else
		for userId, data in pairs(playerItemData) do
			print("玩家: " .. data.playerName .. " (ID:" .. userId .. ")")
			print("  职业ID: " .. data.occupationId)
			print("  物品数量: " .. #data.items)
			for i, item in ipairs(data.items) do
				print(string.format("    物品%d: ID=%d, 数量=%d, 类型=%d", i, item.id, item.quantity, item.itemType))
			end
		end
	end
	print("=== 传送数据检查结束 ===")

	local teleportOptions = Instance.new("TeleportOptions")
	teleportOptions:SetTeleportData({
		sourcePlace = game.PlaceId,
		timestamp = os.time(),
		-- 移除硬编码的items，改为使用动态的玩家物品数据
		playerItemData = playerItemData,  -- 新的玩家物品数据表
		professionPlayer = ProfessionManager.playerProfession,
		professionState = ProfessionManager.playerProfessionState
	})

	local success, err = pcall(function()
		-- 第二个参数就是玩家数组，符合 TeleportAsync 要求
		TeleportService:TeleportAsync(TARGET_PLACE_ID, players, teleportOptions)
	end)

	print("传输数据成功")
	if not success then
		warn("传送失败:", err)
	end
end

return TeleportManager