--!strict
-- 测试运行脚本
-- 在服务器端运行各种测试

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 等待所有必要的服务加载
wait(2)

print("🧪 开始运行测试套件...")

-- 运行传送数据测试
local TeleportDataTest = require(ReplicatedStorage.Scripts.Test.TeleportDataTest)

spawn(function()
	wait(1)
	print("\n" .. "="*50)
	print("🔬 传送数据系统测试")
	print("="*50)
	
	TeleportDataTest.runAllTests()
	TeleportDataTest.testDataValidation()
	
	print("="*50)
	print("🏁 测试套件运行完成")
	print("="*50 .. "\n")
end)
