# 职业系统功能实现说明

## 概述
本次更新为战斗系统添加了完整的职业物品管理功能，实现了从职业选择到物品数据传送的完整流程。

## 新增功能

### 1. OccupationConfig 解析器 (`Server/Utils/OccupationConfigParser.lua`)
- **功能**: 解析 OccupationConfig 中的 CarryingItem 字段
- **格式**: `数量_类型_物品ID` (例如: `2_7_10026;50_6_10029;1_10_10051`)
- **主要方法**:
  - `getOccupationConfig(occupationId)`: 获取职业配置
  - `getItemsByOccupationId(occupationId)`: 获取职业对应的物品数据
  - `validateCarryingItemFormat(carryingItemStr)`: 验证物品字符串格式
  - `printOccupationItems(occupationId)`: 打印物品数据（调试用）

### 2. 玩家物品数据管理器 (`Server/Manager/PlayerItemManager.lua`)
- **功能**: 管理玩家的职业物品数据
- **数据结构**: `{[userId] = {playerName, occupationId, items, timestamp}}`
- **主要方法**:
  - `setPlayerOccupationItems(player, occupationId)`: 设置玩家职业物品
  - `getPlayerItemData(userId)`: 获取玩家物品数据
  - `getTeleportItemData()`: 获取传送格式的数据
  - `printAllPlayerItemData()`: 打印所有玩家数据（调试用）

### 3. ProfessionManager 集成
- **修改**: 在 `changeModel` 函数中集成物品数据处理
- **流程**: 职业切换 → 调用 PlayerItemManager 设置物品数据
- **初始化**: 自动初始化 PlayerItemManager

### 4. TeleportManager 数据传送
- **修改**: 传送数据中包含玩家物品数据
- **新增字段**: `playerItemData` - 包含所有玩家的物品信息
- **调试功能**: 传送前打印详细的数据信息

## 数据流程

```
1. 玩家选择职业 (ProfessionSwitch)
   ↓
2. 调用 ProfessionManager.changeModel()
   ↓
3. 解析 OccupationConfig 获取物品数据 (OccupationConfigParser)
   ↓
4. 存储玩家物品数据 (PlayerItemManager)
   ↓
5. 传送时包含物品数据 (TeleportManager)
```

## 配置文件更新

### OccupationConfig 格式
```lua
{
  Id = 4001,
  Chinese = "战士",
  Name = "Warrior",
  CarryingItem = "2_7_10026;50_6_10029;1_10_10051",
  -- 其他字段...
}
```

### CarryingItem 格式说明
- 格式: `数量_类型_物品ID`
- 多个物品用分号分隔: `物品1;物品2;物品3`
- 示例: `2_7_10026;50_6_10029;1_10_10051`
  - 物品1: 数量2, 类型7, ID10026
  - 物品2: 数量50, 类型6, ID10029
  - 物品3: 数量1, 类型10, ID10051

## 传送数据结构

```lua
teleportData = {
  sourcePlace = game.PlaceId,
  timestamp = os.time(),
  playerItemData = {
    [userId] = {
      playerName = "玩家名字",
      occupationId = 4001,
      items = {
        {id = 10026, quantity = 2, itemType = 7},
        {id = 10029, quantity = 50, itemType = 6},
        {id = 10051, quantity = 1, itemType = 10}
      }
    }
  },
  professionPlayer = {...},
  professionState = {...}
}
```

## 测试功能

### 测试脚本 (`Server/Utils/TestOccupationSystem.lua`)
- 完整的功能测试套件
- 包含单元测试和集成测试
- 自动验证数据完整性

### 运行测试
```lua
local TestOccupationSystem = require(ReplicatedStorage.Scripts.Server.Utils.TestOccupationSystem)
TestOccupationSystem.runAllTests()
```

## 调试功能

### 数据打印
- `OccupationConfigParser.printOccupationItems()`: 打印职业物品
- `PlayerItemManager.printAllPlayerItemData()`: 打印玩家数据
- `TeleportManager.teleportPlayers()`: 传送前自动打印数据

### 数据验证
- 物品字符串格式验证
- 玩家数据完整性验证
- 错误信息详细输出

## 使用示例

### 1. 获取职业物品数据
```lua
local items = OccupationConfigParser.getItemsByOccupationId(4001)
for _, item in ipairs(items) do
    print("物品ID:", item.id, "数量:", item.quantity, "类型:", item.itemType)
end
```

### 2. 设置玩家物品
```lua
local success = PlayerItemManager.setPlayerOccupationItems(player, 4001)
if success then
    print("物品数据设置成功")
end
```

### 3. 获取传送数据
```lua
local teleportData = PlayerItemManager.getTeleportItemData()
-- 传送数据已准备好，包含所有玩家的物品信息
```

## 注意事项

1. **数据同步**: 玩家离开游戏时自动清理数据
2. **错误处理**: 所有函数都包含完整的错误检查
3. **性能优化**: 使用索引表提高查询效率
4. **调试支持**: 详细的日志输出便于问题排查
5. **数据验证**: 确保传送数据的完整性和正确性

## 后续扩展

- 可以轻松添加新的职业配置
- 支持动态物品数据修改
- 可扩展物品属性和效果
- 支持更复杂的物品组合逻辑
